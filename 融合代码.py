from maix import image, camera, display, app, time
import cv2
import numpy as np

# ==================== 参数区 ====================

class Config:
    """配置参数类"""
    # 硬件参数
    CAMERA_WIDTH = 1024
    CAMERA_HEIGHT = 720
    
    # 图像处理参数
    BINARY_THRESHOLD = 100
    CANNY_LOW = 30
    CANNY_HIGH = 90
    MIN_AREA = 100
    EPSILON_FACTOR = 0.1
    
    # 形状检测参数
    CIRCULARITY_THRESHOLD = 0.85
    DUPLICATE_DISTANCE_THRESHOLD = 15
    DUPLICATE_AREA_RATIO = 0.8
    AREA_OVERLAP_THRESHOLD = 0.7
    POLYGON_OVERLAP_THRESHOLD = 0.7
    
    # 跟踪参数
    POSITION_TOLERANCE = 20
    VERTEX_HISTORY_SIZE = 3
    EDGE_HISTORY_SIZE = 3
    SHAPE_TIMEOUT = 1
    
    # 预处理参数
    ENABLE_PREPROCESS = True
    PREPROCESS_START_FRAME = 3
    PREPROCESS_STABLE_THRESHOLD = 2
    
    # ROI优化参数
    USE_ROI_OPTIMIZATION = True
    ROI_EXPAND_PIXELS = 20
    
    # 四边形区域检测参数
    QUAD_MASK_EXPAND_PIXELS = 10  # 四边形掩码扩展像素数
    
    # 距离测量参数（请根据实际情况修改）
    ENABLE_DISTANCE_MEASUREMENT = True
    CALIBRATION_DISTANCE = 150.0  # 标定时的实际距离（厘米）
    CALIBRATION_PIXELS = 334.0    # 标定时最大四边形最长边的像素值
    MAX_RECT_PHYSICAL_LENGTH = 28.2  # 最大矩形的物理长度（厘米）
    DISTANCE_HISTORY_SIZE = 5
    
    # 框内统计参数
    ENABLE_INNER_SHAPE_STATS = True

    # 拐点处理参数
    ENABLE_CORNER_PROCESSING = True
    CORNER_OVERLAP_THRESHOLD = 0.5
    ENABLE_ISOLATED_CORNER_DETECTION = True
    SHOW_CORNER_ANALYSIS = True

    # 显示参数
    SHOW_DEBUG = True
    DEBUG_VIEW = 2  # 0:原图 1:二值 2:边缘
    SHOW_EDGE_LENGTHS = True
    SHOW_SHAPE_AREAS = False
    SHOW_INNER_SHAPE_STATS = True
    USE_INSTANT_VALUES = True  # 使用当前测量值而非平均值
    SHOW_FILTER_DEBUG = True  # 是否显示过滤调试信息

class GlobalState:
    """全局状态管理"""
    def __init__(self):
        self.frame_count = 0
        self.view_switch_count = 0
        self.current_distance = 0.0
        self.distance_history = []
        self.second_largest_rect_info = None
        
        # 预处理状态
        self.max_rectangles = []  # 改为只存储一个最大矩形
        self.preprocess_started = False
        self.preprocess_stable_frames = 0
        
        # 缓存状态
        self.cached_mask = None
        self.cached_mask_valid = False
        self.last_rectangles_centers = []
        self.cached_roi_rect = None
        self.roi_valid = False
        
        # 跟踪数据
        self.shape_tracking_data = {}
        self.vertex_history = {}
        self.edge_history = {}
        self.circle_radius_history = {}
        self.last_frame_shapes = {}
        
        # 统计数据
        self.inner_shapes_stats = {
            'triangles': {'count': 0, 'avg_edge_length': 0.0, 'total_edge_length': 0.0,
                         'avg_edge_length_physical': 0.0, 'total_edge_length_physical': 0.0},
            'quadrilaterals': {'count': 0, 'avg_edge_length': 0.0, 'total_edge_length': 0.0,
                              'avg_edge_length_physical': 0.0, 'total_edge_length_physical': 0.0},
            'circles': {'count': 0, 'avg_radius': 0.0, 'total_radius': 0.0,
                       'avg_radius_physical': 0.0, 'total_radius_physical': 0.0},
            'polygons': {'count': 0, 'avg_edge_length': 0.0, 'total_edge_length': 0.0,
                        'avg_edge_length_physical': 0.0, 'total_edge_length_physical': 0.0}
        }
        
        # 形状计数历史记录
        self.triangle_counts = []
        self.quadrilateral_counts = []
        self.circle_counts = []
        self.polygon_counts = []
        
        # 矩形尺寸历史记录
        self.max_rect1_sizes = []
        self.max_rect2_sizes = []

        # 拐点处理相关状态
        self.corner_analysis_results = {}  # 存储拐点分析结果
        self.noise_points_cache = []  # 噪声点缓存

# 全局实例
config = Config()
state = GlobalState()


class UtilityFunctions:
    """实用函数类"""
    @staticmethod
    def get_value_from_history(values, use_latest=True):
        """从历史记录中获取值，默认使用最新值"""
        if not values:
            return 0
        return values[-1] if use_latest or len(values) < 2 else int(sum(values[-3:]) / min(len(values), 3))
    
    @staticmethod
    def is_point_inside_polygon(point, polygon_points):
        """判断点是否在多边形内部"""
        try:
            if polygon_points is None or len(polygon_points) < 3:
                return False
            
            # 转换格式
            if isinstance(polygon_points, np.ndarray):
                if len(polygon_points.shape) == 3:
                    polygon_contour = polygon_points.astype(np.int32)
                elif len(polygon_points.shape) == 2:
                    polygon_contour = polygon_points.reshape(-1, 1, 2).astype(np.int32)
                else:
                    return False
            else:
                try:
                    polygon_contour = np.array(polygon_points, dtype=np.int32).reshape(-1, 1, 2)
                except Exception:
                    return False
            
            px, py = float(point[0]), float(point[1])
            result = cv2.pointPolygonTest(polygon_contour, (px, py), False)
            return result >= 0
            
        except Exception as e:
            print(f"判断点是否在多边形内出错: {e}")
            return False
    
    @staticmethod
    def collect_fine_polygons_from_quads(quadrilaterals, img_cv, edges, min_area):
        """从所有四边形区域收集精细检测的多边形"""
        collected_polygons = []
        
        # 只在预处理完成后才进行多边形检测
        if not (config.ENABLE_PREPROCESS and state.preprocess_started and 
                len(state.max_rectangles) == 1 and 
                state.preprocess_stable_frames >= config.PREPROCESS_STABLE_THRESHOLD):
            return collected_polygons
        
        fine_epsilon_factor = 0.01
        
        try:
            for quad_info in quadrilaterals:
                quad_approx = quad_info['approx']
                
                # 创建四边形区域的掩码
                if edges.shape != img_cv.shape[:2]:
                    # ROI模式：调整坐标
                    if state.roi_valid and state.cached_roi_rect:
                        roi_x, roi_y, roi_w, roi_h = state.cached_roi_rect
                        adjusted_quad = quad_approx.copy()
                        adjusted_quad[:, 0, 0] -= roi_x
                        adjusted_quad[:, 0, 1] -= roi_y
                        
                        mask = np.zeros(edges.shape, dtype=np.uint8)
                        cv2.fillPoly(mask, [adjusted_quad], 255)
                    else:
                        continue
                else:
                    # 非ROI模式
                    mask = np.zeros(edges.shape, dtype=np.uint8)
                    cv2.fillPoly(mask, [quad_approx], 255)
                
                # 在四边形区域内查找轮廓
                masked_edges = cv2.bitwise_and(edges, mask)
                contours, _ = cv2.findContours(masked_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                for contour in contours:
                    area = cv2.contourArea(contour)
                    if area < min_area:
                        continue
                    
                    # 使用精细的epsilon_factor
                    epsilon = fine_epsilon_factor * cv2.arcLength(contour, True)
                    approx = cv2.approxPolyDP(contour, epsilon, True)
                    num_vertices = len(approx)
                    
                    # 只收集多边形（5个或更多顶点）
                    if num_vertices >= 5:
                        M = cv2.moments(contour)
                        if M["m00"] != 0:
                            cx = int(M["m10"] / M["m00"])
                            cy = int(M["m01"] / M["m00"])
                            
                            # 如果是ROI模式，需要转换回原图坐标
                            if edges.shape != img_cv.shape[:2] and state.roi_valid and state.cached_roi_rect:
                                roi_x, roi_y, roi_w, roi_h = state.cached_roi_rect
                                cx += roi_x
                                cy += roi_y
                                
                                # 调整approx坐标
                                adjusted_approx = approx.copy()
                                adjusted_approx[:, 0, 0] += roi_x
                                adjusted_approx[:, 0, 1] += roi_y
                                approx = adjusted_approx
                            
                            # 确保坐标格式正确
                            formatted_approx = np.array([[int(pt[0][0]), int(pt[0][1])] for pt in approx], dtype=np.int32)
                            formatted_approx = formatted_approx.reshape(-1, 1, 2)
                            
                            collected_polygons.append((cx, cy, formatted_approx))
        
        except Exception as e:
            print(f"收集精细多边形时出错: {e}")
        
        return collected_polygons
    
    @staticmethod
    def calculate_distance_between_shapes(shape1, shape2):
        """计算两个形状之间的距离（以像素为单位）"""
        try:
            # 获取形状的中心点
            if isinstance(shape1, tuple) and len(shape1) >= 2:
                center1 = (shape1[0], shape1[1])
            else:
                return float('inf')
            
            if isinstance(shape2, tuple) and len(shape2) >= 2:
                center2 = (shape2[0], shape2[1])
            else:
                return float('inf')
            
            # 计算欧几里得距离
            distance = ((center1[0] - center2[0]) ** 2 + (center1[1] - center2[1]) ** 2) ** 0.5
            return distance
        except:
            return float('inf')
    
    @staticmethod
    def check_shapes_overlap(shape1_contour, shape2_contour):
        """检查两个形状是否重叠"""
        try:
            # 计算轮廓的边界框
            x1, y1, w1, h1 = cv2.boundingRect(shape1_contour)
            x2, y2, w2, h2 = cv2.boundingRect(shape2_contour)

            # 检查边界框是否重叠
            if (x1 < x2 + w2 and x1 + w1 > x2 and
                y1 < y2 + h2 and y1 + h1 > y2):
                # 如果边界框重叠，进一步检查轮廓重叠
                # 创建掩码
                mask_size = (max(x1 + w1, x2 + w2) + 10, max(y1 + h1, y2 + h2) + 10)
                mask1 = np.zeros(mask_size, dtype=np.uint8)
                mask2 = np.zeros(mask_size, dtype=np.uint8)

                # 绘制轮廓到掩码
                cv2.fillPoly(mask1, [shape1_contour], 255)
                cv2.fillPoly(mask2, [shape2_contour], 255)

                # 检查交集
                intersection = cv2.bitwise_and(mask1, mask2)
                return cv2.countNonZero(intersection) > 0

            return False
        except:
            return False

    @staticmethod
    def calculate_polygon_area(points):
        """
        使用鞋带公式计算多边形面积

        参数:
        - points: 多边形顶点列表 [(x1, y1), (x2, y2), ...]

        返回:
        - area: 多边形面积
        """
        if len(points) < 3:
            return 0.0

        area = 0.0
        n = len(points)
        for i in range(n):
            j = (i + 1) % n
            area += points[i][0] * points[j][1]
            area -= points[j][0] * points[i][1]

        return abs(area) / 2.0

    @staticmethod
    def point_to_line_distance(point, line_start, line_end):
        """
        计算点到直线的垂直距离

        参数:
        - point: 点坐标 (x, y)
        - line_start: 直线起点 (x, y)
        - line_end: 直线终点 (x, y)

        返回:
        - distance: 垂直距离
        """
        x0, y0 = point
        x1, y1 = line_start
        x2, y2 = line_end

        # 如果直线起点和终点相同，返回点到点的距离
        if x1 == x2 and y1 == y2:
            return MathUtils.calculate_distance(point, line_start)

        # 使用点到直线距离公式: |ax0 + by0 + c| / sqrt(a^2 + b^2)
        # 直线方程: (y2-y1)x - (x2-x1)y + (x2-x1)y1 - (y2-y1)x1 = 0
        a = y2 - y1
        b = -(x2 - x1)
        c = (x2 - x1) * y1 - (y2 - y1) * x1

        distance = abs(a * x0 + b * y0 + c) / np.sqrt(a * a + b * b)
        return distance

    @staticmethod
    def is_point_near_line_segment(point, line_start, line_end, threshold=5.0):
        """
        判断点是否在线段附近（考虑阈值）

        参数:
        - point: 点坐标 (x, y)
        - line_start: 线段起点 (x, y)
        - line_end: 线段终点 (x, y)
        - threshold: 距离阈值

        返回:
        - bool: 是否在线段附近
        """
        # 计算点到线段的最短距离
        x0, y0 = point
        x1, y1 = line_start
        x2, y2 = line_end

        # 线段长度的平方
        line_length_sq = (x2 - x1) ** 2 + (y2 - y1) ** 2

        if line_length_sq == 0:
            # 线段退化为点
            return MathUtils.calculate_distance(point, line_start) <= threshold

        # 计算投影参数t
        t = max(0, min(1, ((x0 - x1) * (x2 - x1) + (y0 - y1) * (y2 - y1)) / line_length_sq))

        # 计算线段上最近的点
        closest_x = x1 + t * (x2 - x1)
        closest_y = y1 + t * (y2 - y1)

        # 计算距离
        distance = MathUtils.calculate_distance(point, (closest_x, closest_y))

        return distance <= threshold


class DistanceCalculator:
    """距离计算类"""
    
    @staticmethod
    def calculate_real_size(pixel_distance):
        """根据像素距离计算真实尺寸（毫米）"""
        try:
            # 获取参考矩形的最长边像素值
            if len(state.max_rectangles) < 1:
                return "未知"
            
            # 获取最大矩形的最长边作为参考
            reference_pixels = DistanceCalculator.get_max_rect_longest_edge()
            
            if reference_pixels <= 0:
                return "未知"
            
            # 使用最大矩形的物理长度作为参考
            real_size_mm = (pixel_distance * config.MAX_RECT_PHYSICAL_LENGTH) / reference_pixels
            
            return f"{real_size_mm:.1f}mm"
        except:
            return "未知"
    
    @staticmethod
    def get_max_rect_longest_edge():
        """获取最大矩形的最长边长度"""
        if len(state.max_rectangles) < 1:
            return 0.0
        
        try:
            # 获取最大矩形的轮廓 - 原始结构是 (contour, area, approx)
            _, _, approx_points = state.max_rectangles[0]
            
            # 计算所有边的长度
            edge_lengths = []
            for i in range(len(approx_points)):
                p1 = approx_points[i][0]
                p2 = approx_points[(i + 1) % len(approx_points)][0]
                edge_length = ((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)**0.5
                edge_lengths.append(edge_length)
            
            # 返回最长边
            return max(edge_lengths) if edge_lengths else 0.0
        except:
            return 0.0
    
    @staticmethod
    def calculate_distance_to_max_rects(shape_center):
        """计算形状到最大矩形的距离"""
        distances = {}
        
        if len(state.max_rectangles) >= 1:
            # 计算最大矩形的中心
            _, _, approx1 = state.max_rectangles[0]
            M1 = cv2.moments(approx1)
            if M1["m00"] != 0:
                rect1_center = (int(M1["m10"] / M1["m00"]), int(M1["m01"] / M1["m00"]))
                dist1 = ((shape_center[0] - rect1_center[0])**2 + (shape_center[1] - rect1_center[1])**2)**0.5
                distances['max_rect'] = {
                    'pixel': int(dist1),
                    'real': DistanceCalculator.calculate_real_size(dist1)
                }
        
        return distances


class StatisticsManager:
    """统计管理类"""
    
    @staticmethod
    def update_shape_statistics(triangles, quadrilaterals, circles, polygons):
        """更新形状统计"""
        state.triangle_counts.append(len(triangles))
        state.quadrilateral_counts.append(len(quadrilaterals))
        state.circle_counts.append(len(circles))
        state.polygon_counts.append(len(polygons))
        
        # 保持历史记录长度
        max_history = 30
        if len(state.triangle_counts) > max_history:
            state.triangle_counts.pop(0)
        if len(state.quadrilateral_counts) > max_history:
            state.quadrilateral_counts.pop(0)
        if len(state.circle_counts) > max_history:
            state.circle_counts.pop(0)
        if len(state.polygon_counts) > max_history:
            state.polygon_counts.pop(0)
    
    @staticmethod
    def get_average_counts():
        """获取平均计数"""
        return {
            'triangles': UtilityFunctions.get_value_from_history(state.triangle_counts, False),
            'quadrilaterals': UtilityFunctions.get_value_from_history(state.quadrilateral_counts, False),
            'circles': UtilityFunctions.get_value_from_history(state.circle_counts, False),
            'polygons': UtilityFunctions.get_value_from_history(state.polygon_counts, False)
        }
    
    @staticmethod
    def print_detailed_statistics():
        """打印详细统计信息"""
        if state.frame_count % 30 == 0:  # 每30帧打印一次
            avg_counts = StatisticsManager.get_average_counts()
            print(f"\n=== 第{state.frame_count}帧统计 ===")
            print(f"平均形状数量: 三角形={avg_counts['triangles']}, 四边形={avg_counts['quadrilaterals']}, 圆形={avg_counts['circles']}, 多边形={avg_counts['polygons']}")
            
            if len(state.max_rectangles) >= 1:
                rect1_size = UtilityFunctions.get_value_from_history(state.max_rect1_sizes, False)
                print(f"参考矩形大小: 矩形={rect1_size}px")
            
            if state.roi_valid:
                print(f"ROI状态: 激活 {state.cached_roi_rect}")
            else:
                print("ROI状态: 未激活")


# ==================== 功能函数区 ====================

class MathUtils:
    """数学计算工具类"""
    @staticmethod
    def calculate_distance(point1, point2):
        """计算两点间距离"""
        return np.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)
    
    @staticmethod
    def calculate_area_ratio(area1, area2):
        """计算面积比例"""
        max_area = max(area1, area2)
        return min(area1, area2) / max_area if max_area > 0 else 1.0
    
    @staticmethod
    def calculate_longest_edge(vertices):
        """计算最长边"""
        if len(vertices) < 2:
            return 0
        max_length = 0
        for i in range(len(vertices)):
            pt1, pt2 = vertices[i], vertices[(i+1) % len(vertices)]
            edge_length = MathUtils.calculate_distance(pt1, pt2)
            max_length = max(max_length, edge_length)
        return max_length
    
    @staticmethod
    def calculate_average_edge_length(vertices):
        """计算平均边长"""
        if len(vertices) < 2:
            return 0.0
        total_length = 0
        for i in range(len(vertices)):
            pt1, pt2 = vertices[i], vertices[(i+1) % len(vertices)]
            total_length += MathUtils.calculate_distance(pt1, pt2)
        return total_length / len(vertices)
    
    @staticmethod
    def calculate_physical_size(pixel_size, reference_pixels, reference_physical_size):
        """根据参考尺寸计算物理尺寸"""
        return (pixel_size * reference_physical_size) / reference_pixels if reference_pixels > 0 else 0.0

class ImageProcessor:
    """图像处理工具类"""
    @staticmethod
    def preprocess_image(img_cv, roi_rect=None):
        """图像预处理"""
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        
        # ROI优化
        if roi_rect and config.USE_ROI_OPTIMIZATION and state.roi_valid:
            x, y, w, h = roi_rect
            gray_roi = gray[y:y+h, x:x+w]
            roi_offset = (x, y)
        else:
            gray_roi = gray
            roi_offset = (0, 0)
        
        # 二值化和边缘检测
        _, binary = cv2.threshold(gray_roi, config.BINARY_THRESHOLD, 255, cv2.THRESH_BINARY)
        edges = cv2.Canny(binary, config.CANNY_LOW, config.CANNY_HIGH)
        
        return gray, binary, edges, roi_offset
    
    @staticmethod
    def find_contours_with_roi_adjustment(edges, roi_offset):
        """查找轮廓并调整ROI坐标"""
        contours, _ = cv2.findContours(edges, cv2.RETR_TREE, cv2.CHAIN_APPROX_NONE)
        
        # 调整ROI坐标
        if roi_offset != (0, 0):
            roi_x, roi_y = roi_offset
            adjusted_contours = []
            for contour in contours:
                adjusted_contour = contour.copy()
                adjusted_contour[:, 0, 0] += roi_x
                adjusted_contour[:, 0, 1] += roi_y
                adjusted_contours.append(adjusted_contour)
            return adjusted_contours
        
        return contours

class ShapeDetector:
    """形状检测器"""
    @staticmethod
    def classify_shape(contour, epsilon_factor=None):
        """分类形状"""
        if epsilon_factor is None:
            epsilon_factor = config.EPSILON_FACTOR
            
        area = cv2.contourArea(contour)
        if area < config.MIN_AREA:
            return None
        
        # 计算中心点
        M = cv2.moments(contour)
        if M["m00"] == 0:
            return None
        cx, cy = int(M["m10"] / M["m00"]), int(M["m01"] / M["m00"])
        
        # 轮廓近似
        epsilon = epsilon_factor * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        num_vertices = len(approx)
        
        # 圆形检测
        perimeter = cv2.arcLength(contour, True)
        circularity = 4 * np.pi * area / (perimeter * perimeter) if perimeter > 0 else 0
        
        shape_info = {
            'type': None,
            'approx': approx,
            'cx': cx,
            'cy': cy,
            'area': area,
            'vertices_count': num_vertices
        }
        
        if circularity > config.CIRCULARITY_THRESHOLD:
            shape_info['type'] = 'Circle'
            shape_info['radius'] = int(np.sqrt(area / np.pi))
            shape_info['vertices'] = None
        elif num_vertices == 3:
            shape_info['type'] = 'Triangle'
            shape_info['vertices'] = [tuple(pt[0]) for pt in approx]
        elif num_vertices == 4:
            shape_info['type'] = 'Quad'
            shape_info['vertices'] = [tuple(pt[0]) for pt in approx]
        elif num_vertices >= 5:
            shape_info['type'] = 'Polygon'
            shape_info['vertices'] = [tuple(pt[0]) for pt in approx]
        
        return shape_info
    
    @staticmethod
    def is_duplicate_shape(shape_info, detected_shapes):
        """检查重复形状"""
        cx, cy, area = shape_info['cx'], shape_info['cy'], shape_info['area']
        vertices_count = shape_info['vertices_count']
        
        for detected_cx, detected_cy, detected_area, detected_vertices in detected_shapes:
            if detected_vertices == vertices_count:
                distance = MathUtils.calculate_distance((cx, cy), (detected_cx, detected_cy))
                area_ratio = MathUtils.calculate_area_ratio(area, detected_area)
                if (distance < config.DUPLICATE_DISTANCE_THRESHOLD and 
                    area_ratio > config.DUPLICATE_AREA_RATIO):
                    return True
        return False

class OverlapCalculator:
    """重叠计算器"""
    @staticmethod
    def calculate_overlap_area(shape1_approx, shape2_approx, canvas_size=800):
        """计算两个形状的重叠面积比例"""
        try:
            # 计算第一个形状面积
            area1 = cv2.contourArea(shape1_approx)
            if area1 <= 0:
                return 0.0
            
            # 处理坐标
            points1 = shape1_approx.reshape(-1, 2)
            points2 = shape2_approx.reshape(-1, 2)
            all_points = np.vstack([points1, points2])
            
            min_x, min_y = np.min(all_points, axis=0)
            max_x, max_y = np.max(all_points, axis=0)
            
            # 动态画布大小
            shape_width, shape_height = max_x - min_x, max_y - min_y
            canvas_size = max(canvas_size, int(max(shape_width, shape_height) * 2 + 100))
            
            # 计算偏移
            offset_x = canvas_size // 2 - (min_x + max_x) // 2
            offset_y = canvas_size // 2 - (min_y + max_y) // 2
            
            # 调整坐标并绘制掩码
            mask1 = np.zeros((canvas_size, canvas_size), dtype=np.uint8)
            mask2 = np.zeros((canvas_size, canvas_size), dtype=np.uint8)
            
            adjusted_points1 = points1 + [offset_x, offset_y]
            adjusted_points2 = points2 + [offset_x, offset_y]
            
            cv2.fillPoly(mask1, [adjusted_points1.astype(np.int32)], 255)
            cv2.fillPoly(mask2, [adjusted_points2.astype(np.int32)], 255)
            
            # 计算重叠
            overlap_mask = cv2.bitwise_and(mask1, mask2)
            overlap_area = cv2.countNonZero(overlap_mask)
            shape1_pixels = cv2.countNonZero(mask1)
            
            return overlap_area / shape1_pixels if shape1_pixels > 0 else 0.0
            
        except Exception as e:
            print(f"计算重叠面积出错: {e}")
            return 0.0
    
    @staticmethod
    def calculate_circle_quad_overlap(quad_approx, circle_cx, circle_cy, circle_radius):
        """计算四边形与圆形重叠（保持向后兼容）"""
        return OverlapCalculator.calculate_circle_polygon_overlap(
            quad_approx, circle_cx, circle_cy, circle_radius
        )
    
    @staticmethod
    def calculate_circle_polygon_overlap(polygon_approx, circle_cx, circle_cy, circle_radius):
        """计算任意多边形与圆形的重叠比例（基于多边形面积）"""
        try:
            polygon_area = cv2.contourArea(polygon_approx)
            if polygon_area <= 0:
                return 0.0
            
            # 创建足够大的画布
            canvas_size = max(800, int(circle_radius * 3))
            offset_x = canvas_size // 2 - circle_cx
            offset_y = canvas_size // 2 - circle_cy
            
            # 调整多边形坐标
            adjusted_polygon = polygon_approx.copy()
            adjusted_polygon[:, 0, 0] += offset_x
            adjusted_polygon[:, 0, 1] += offset_y
            
            # 绘制掩码
            polygon_mask = np.zeros((canvas_size, canvas_size), dtype=np.uint8)
            circle_mask = np.zeros((canvas_size, canvas_size), dtype=np.uint8)
            
            # 绘制多边形
            polygon_points = adjusted_polygon.reshape(-1, 2)
            cv2.fillPoly(polygon_mask, [polygon_points], 255)
            
            # 绘制圆形
            cv2.circle(circle_mask, (canvas_size // 2, canvas_size // 2), circle_radius, 255, -1)
            
            # 计算重叠
            overlap_mask = cv2.bitwise_and(polygon_mask, circle_mask)
            overlap_area = cv2.countNonZero(overlap_mask)
            polygon_pixels = cv2.countNonZero(polygon_mask)
            
            return overlap_area / polygon_pixels if polygon_pixels > 0 else 0.0
            
        except Exception as e:
            print(f"计算圆形多边形重叠出错: {e}")
            return 0.0

class ShapeFilter:
    """形状过滤器 - 基于优先级的泛用过滤体系"""
    
    # 形状优先级定义（数字越高优先级越高）
    SHAPE_PRIORITY = {
        'Polygon': 4,   # 最高优先级
        'Circle': 3,    # 第二优先级
        'Quad': 2,      # 第三优先级
        'Triangle': 1   # 最低优先级
    }
    
    @staticmethod
    def apply_priority_based_filtering(shape_candidates):
        """基于优先级的形状过滤系统"""
        if config.SHOW_FILTER_DEBUG:
            print("🔍 开始优先级过滤...")
        
        # 创建所有形状的列表并按优先级排序
        all_shapes = []
        for shape_type, shapes in shape_candidates.items():
            for shape_info in shapes:
                shape_info['priority'] = ShapeFilter.SHAPE_PRIORITY.get(shape_type, 0)
                all_shapes.append(shape_info)
        
        # 按优先级从高到低排序
        all_shapes.sort(key=lambda x: x['priority'], reverse=True)
        
        # 保留的形状列表
        filtered_shapes = {'Triangle': [], 'Quad': [], 'Circle': [], 'Polygon': []}
        
        # 从高优先级到低优先级依次处理
        for current_shape in all_shapes:
            should_keep = True
            current_type = current_shape['type']
            
            # 检查是否被更高优先级的形状过滤
            for kept_shape_type, kept_shapes in filtered_shapes.items():
                kept_priority = ShapeFilter.SHAPE_PRIORITY.get(kept_shape_type, 0)
                current_priority = current_shape['priority']
                
                # 只检查优先级更高或相等的已保留形状
                if kept_priority >= current_priority:
                    for kept_shape in kept_shapes:
                        if ShapeFilter._check_overlap_and_filter(current_shape, kept_shape):
                            if config.SHOW_FILTER_DEBUG:
                                print(f"  ❌ {current_type} 被 {kept_shape_type} 过滤 (重叠度过高)")
                            should_keep = False
                            break
                
                if not should_keep:
                    break
            
            # 如果通过过滤，保留该形状
            if should_keep:
                filtered_shapes[current_type].append(current_shape)
                if config.SHOW_FILTER_DEBUG:
                    print(f"  ✅ 保留 {current_type} (优先级: {current_shape['priority']})")
        
        return filtered_shapes
    
    @staticmethod
    def _check_overlap_and_filter(shape1, shape2):
        """检查两个形状是否重叠并决定是否过滤"""
        try:
            # 获取形状类型
            type1, type2 = shape1['type'], shape2['type']
            
            # 计算重叠度
            overlap_ratio = 0.0
            
            # 圆形与其他形状的重叠计算
            if type1 == 'Circle' and type2 in ['Triangle', 'Quad', 'Polygon']:
                overlap_ratio = OverlapCalculator.calculate_circle_polygon_overlap(
                    shape2['approx'], shape1['cx'], shape1['cy'], shape1['radius']
                )
            elif type2 == 'Circle' and type1 in ['Triangle', 'Quad', 'Polygon']:
                overlap_ratio = OverlapCalculator.calculate_circle_polygon_overlap(
                    shape1['approx'], shape2['cx'], shape2['cy'], shape2['radius']
                )
            
            # 多边形形状之间的重叠计算（包括三角形、四边形、多边形）
            elif type1 in ['Triangle', 'Quad', 'Polygon'] and type2 in ['Triangle', 'Quad', 'Polygon']:
                overlap_ratio = OverlapCalculator.calculate_overlap_area(
                    shape1['approx'], shape2['approx']
                )
            
            # 两个圆形之间的重叠（如果需要）
            elif type1 == 'Circle' and type2 == 'Circle':
                # 圆形之间的重叠可以通过距离和半径计算
                distance = MathUtils.calculate_distance(
                    (shape1['cx'], shape1['cy']), 
                    (shape2['cx'], shape2['cy'])
                )
                r1, r2 = shape1['radius'], shape2['radius']
                if distance < r1 + r2:  # 如果圆形相交
                    # 简化处理：如果中心距离小于较大半径，认为有显著重叠
                    overlap_ratio = max(0, 1.0 - distance / max(r1, r2))
                else:
                    overlap_ratio = 0.0
            
            # 判断是否需要过滤
            threshold = config.AREA_OVERLAP_THRESHOLD
            if overlap_ratio >= threshold:
                if config.SHOW_FILTER_DEBUG:
                    print(f"    📊 {type1} vs {type2} 重叠度: {overlap_ratio:.3f} >= {threshold} (过滤)")
                return True
            else:
                if config.SHOW_FILTER_DEBUG:
                    print(f"    📊 {type1} vs {type2} 重叠度: {overlap_ratio:.3f} < {threshold} (保留)")
                return False
                
        except Exception as e:
            print(f"重叠检查出错: {e}")
            return False
    
    @staticmethod
    def should_filter_shape(shape_info, detected_circles, detected_polygons):
        """旧版本兼容方法 - 已被优先级过滤替代"""
        # 保留此方法以确保向后兼容，但实际不再使用
        return False

class ShapeTracker:
    """形状跟踪器"""
    @staticmethod
    def find_matching_position(cx, cy, shape_type):
        """查找匹配的形状位置"""
        min_distance = float('inf')
        shape_position = None
        
        for pos in state.shape_tracking_data.keys():
            if pos[0] == shape_type:
                distance = MathUtils.calculate_distance((cx, cy), (pos[1], pos[2]))
                if distance < config.POSITION_TOLERANCE and distance < min_distance:
                    min_distance = distance
                    shape_position = pos
        
        return shape_position
    
    @staticmethod
    def update_tracking(shape_info):
        """更新形状跟踪"""
        cx, cy = shape_info['cx'], shape_info['cy']
        shape_type = shape_info['type']
        vertices = shape_info.get('vertices')
        
        shape_position = ShapeTracker.find_matching_position(cx, cy, shape_type)
        
        if shape_position is None:
            shape_position = (shape_type, cx, cy)
            state.shape_tracking_data[shape_position] = state.frame_count
            if vertices:
                state.vertex_history[shape_position] = [vertices]
        else:
            state.shape_tracking_data[shape_position] = state.frame_count
            if vertices:
                if shape_position in state.vertex_history:
                    state.vertex_history[shape_position].append(vertices)
                    if len(state.vertex_history[shape_position]) > config.VERTEX_HISTORY_SIZE:
                        state.vertex_history[shape_position].pop(0)
                else:
                    state.vertex_history[shape_position] = [vertices]
        
        # 记录到当前帧
        if shape_type not in state.last_frame_shapes:
            state.last_frame_shapes[shape_type] = []
        state.last_frame_shapes[shape_type].append((cx, cy, shape_info['area']))
        
        return shape_position
    
    @staticmethod
    def cleanup_expired_shapes():
        """清理过期形状"""
        expired_positions = []
        for pos, last_seen_frame in state.shape_tracking_data.items():
            if state.frame_count - last_seen_frame > config.SHAPE_TIMEOUT:
                expired_positions.append(pos)
        
        cleaned_count = 0
        for pos in expired_positions:
            state.shape_tracking_data.pop(pos, None)
            state.vertex_history.pop(pos, None)
            state.circle_radius_history.pop(pos, None)
            
            # 清理边长历史
            edge_keys_to_remove = [key for key in state.edge_history.keys() 
                                 if isinstance(key, tuple) and len(key) >= 1 and key[0] == pos]
            for key in edge_keys_to_remove:
                state.edge_history.pop(key, None)
            
            cleaned_count += 1
        
        return cleaned_count

class DistanceMeasurement:
    """距离测量器"""
    @staticmethod
    def is_max_rectangle(quad_info):
        """判断是否为标定的最大矩形"""
        if len(state.max_rectangles) < 1:
            return False
        
        _, max_area, max_approx = state.max_rectangles[0]
        M_max = cv2.moments(max_approx)
        if M_max["m00"] == 0:
            return False
        
        cx_max = int(M_max["m10"] / M_max["m00"])
        cy_max = int(M_max["m01"] / M_max["m00"])
        
        distance = MathUtils.calculate_distance(
            (quad_info['cx'], quad_info['cy']), (cx_max, cy_max)
        )
        area_ratio = MathUtils.calculate_area_ratio(quad_info['area'], max_area)
        
        return (distance < config.DUPLICATE_DISTANCE_THRESHOLD and 
                area_ratio > config.DUPLICATE_AREA_RATIO)
    
    @staticmethod
    def calculate_distance(quad_info):
        """计算距离"""
        if not config.ENABLE_DISTANCE_MEASUREMENT or not DistanceMeasurement.is_max_rectangle(quad_info):
            return None
        
        vertices = quad_info['vertices']
        longest_edge = MathUtils.calculate_longest_edge(vertices)
        
        if longest_edge > 0:
            # 使用反比例关系计算距离
            calculated_distance = (config.CALIBRATION_PIXELS * config.CALIBRATION_DISTANCE) / longest_edge
            
            state.distance_history.append(calculated_distance)
            if len(state.distance_history) > config.DISTANCE_HISTORY_SIZE:
                state.distance_history.pop(0)
            
            state.current_distance = sum(state.distance_history) / len(state.distance_history)
            state.second_largest_rect_info = {
                'center': (quad_info['cx'], quad_info['cy']),
                'longest_edge': longest_edge,
                'distance': state.current_distance,
                'vertices': vertices
            }
            
            return state.current_distance
        
        return None

class StatsUpdater:
    """统计更新器"""
    @staticmethod
    def update_shape_stats(shape_info):
        """更新形状统计"""
        if not config.ENABLE_INNER_SHAPE_STATS:
            return
        
        shape_type = shape_info['type']
        reference_pixels = StatsUpdater._get_reference_pixels()
        
        if shape_type == 'Circle':
            StatsUpdater._update_circle_stats(shape_info, reference_pixels)
        elif shape_type in ['Triangle', 'Quad', 'Polygon']:
            StatsUpdater._update_polygon_stats(shape_info, reference_pixels)
    
    @staticmethod
    def _get_reference_pixels():
        """获取参考像素长度"""
        if len(state.max_rectangles) < 1:
            return 0.0
        _, _, approx = state.max_rectangles[0]
        vertices = [tuple(pt[0]) for pt in approx]
        return MathUtils.calculate_longest_edge(vertices)
    
    @staticmethod
    def _update_circle_stats(shape_info, reference_pixels):
        """更新圆形统计"""
        radius = shape_info['radius']
        radius_physical = MathUtils.calculate_physical_size(
            radius, reference_pixels, config.MAX_RECT_PHYSICAL_LENGTH
        ) if reference_pixels > 0 else 0.0
        
        stats = state.inner_shapes_stats['circles']
        stats['count'] += 1
        stats['total_radius'] += radius
        stats['total_radius_physical'] += radius_physical
        stats['avg_radius'] = stats['total_radius'] / stats['count']
        stats['avg_radius_physical'] = stats['total_radius_physical'] / stats['count']
    
    @staticmethod
    def _update_polygon_stats(shape_info, reference_pixels):
        """更新多边形统计"""
        shape_type = shape_info['type']
        vertices = shape_info['vertices']
        
        if not vertices:
            return
        
        avg_edge_pixels = MathUtils.calculate_average_edge_length(vertices)
        avg_edge_physical = MathUtils.calculate_physical_size(
            avg_edge_pixels, reference_pixels, config.MAX_RECT_PHYSICAL_LENGTH
        ) if reference_pixels > 0 else 0.0
        
        stats_key = {'Triangle': 'triangles', 'Quad': 'quadrilaterals', 'Polygon': 'polygons'}[shape_type]
        stats = state.inner_shapes_stats[stats_key]
        
        stats['count'] += 1
        stats['total_edge_length'] += avg_edge_pixels
        stats['total_edge_length_physical'] += avg_edge_physical
        stats['avg_edge_length'] = stats['total_edge_length'] / stats['count']
        stats['avg_edge_length_physical'] = stats['total_edge_length_physical'] / stats['count']
    
    @staticmethod
    def reset_stats():
        """重置统计数据"""
        for shape_type in state.inner_shapes_stats:
            stats = state.inner_shapes_stats[shape_type]
            for key in stats:
                stats[key] = 0.0 if 'avg' in key or 'total' in key else 0

class Renderer:
    """渲染器"""
    @staticmethod
    def draw_shape(img_result, shape_info, shape_position, is_max_rect=False, is_second_largest=False):
        """绘制形状"""
        cx, cy = shape_info['cx'], shape_info['cy']
        shape_type = shape_info['type']
        
        # 设置颜色，特殊处理四边形
        if shape_type == 'Quad':
            if is_max_rect:
                color = image.COLOR_YELLOW
            else:
                color = image.COLOR_GREEN
        else:
            color_map = {
                'Triangle': image.COLOR_RED,
                'Circle': image.COLOR_BLUE,
                'Polygon': image.COLOR_PURPLE
            }
            color = color_map.get(shape_type, image.COLOR_WHITE)
        
        # 绘制标签
        label = shape_type
        if shape_type == 'Polygon':
            label = f"Polygon({shape_info['vertices_count']})"
        elif shape_type == 'Quad' and is_max_rect:
            label = "Quad(标定)"
        
        img_result.draw_string(cx-20, cy, label, color)
        
        # 绘制面积（如果启用）
        if config.SHOW_SHAPE_AREAS:
            area_text = f"A:{int(shape_info['area'])}"
            img_result.draw_string(cx-20, cy+15, area_text, color)
        
        # 绘制形状特有元素
        if shape_type == 'Circle':
            Renderer._draw_circle(img_result, shape_info, shape_position, color)
        else:
            Renderer._draw_polygon_edges(img_result, shape_info, shape_position, color)
    
    @staticmethod
    def _draw_circle(img_result, shape_info, shape_position, color):
        """绘制圆形"""
        cx, cy, radius = shape_info['cx'], shape_info['cy'], shape_info['radius']
        
        # 处理半径历史
        if shape_position not in state.circle_radius_history:
            state.circle_radius_history[shape_position] = []
        
        state.circle_radius_history[shape_position].append(radius)
        if len(state.circle_radius_history[shape_position]) > config.EDGE_HISTORY_SIZE:
            state.circle_radius_history[shape_position].pop(0)
        
        display_radius = UtilityFunctions.get_value_from_history(
            state.circle_radius_history[shape_position], config.USE_INSTANT_VALUES
        )
        
        img_result.draw_circle(cx, cy, display_radius, color, thickness=2)
        img_result.draw_circle(cx, cy, 3, color, thickness=-1)
        
        if config.SHOW_EDGE_LENGTHS:
            radius_text = f"R:{display_radius}"
            img_result.draw_string(cx + 5, cy, radius_text, image.COLOR_RED)
    
    @staticmethod
    def _draw_polygon_edges(img_result, shape_info, shape_position, color):
        """绘制多边形边缘"""
        vertices = shape_info['vertices']
        if not vertices:
            return
        
        for i in range(len(vertices)):
            pt1, pt2 = vertices[i], vertices[(i+1) % len(vertices)]
            
            # 绘制边
            img_result.draw_line(pt1[0], pt1[1], pt2[0], pt2[1], color, 2)
            img_result.draw_circle(pt1[0], pt1[1], 3, color, thickness=-1)
            
            # 计算并显示边长
            if config.SHOW_EDGE_LENGTHS:
                edge_length = int(MathUtils.calculate_distance(pt1, pt2))
                edge_key = (shape_position, i)
                
                if edge_key not in state.edge_history:
                    state.edge_history[edge_key] = []
                
                state.edge_history[edge_key].append(edge_length)
                if len(state.edge_history[edge_key]) > config.EDGE_HISTORY_SIZE:
                    state.edge_history[edge_key].pop(0)
                
                display_length = UtilityFunctions.get_value_from_history(
                    state.edge_history[edge_key], config.USE_INSTANT_VALUES
                )
                mid_x, mid_y = (pt1[0] + pt2[0]) // 2, (pt1[1] + pt2[1]) // 2
                img_result.draw_string(mid_x, mid_y, str(display_length), image.COLOR_RED)
    
    @staticmethod
    def draw_info_overlay(img_result):
        """绘制信息覆盖层"""
        y_offset = 8
        
        # ROI信息
        if config.USE_ROI_OPTIMIZATION and state.roi_valid:
            x, y, w, h = state.cached_roi_rect
            total_area = config.CAMERA_WIDTH * config.CAMERA_HEIGHT
            roi_percent = (w*h)/total_area*100 if total_area > 0 else 0
            roi_info = f"ROI: {w}x{h} ({roi_percent:.0f}%)"
            img_result.draw_string(8, y_offset, roi_info, image.COLOR_GREEN)
            y_offset += 22
        
        # 距离信息
        if config.ENABLE_DISTANCE_MEASUREMENT and state.current_distance > 0:
            distance_info = f"Distance: {state.current_distance:.1f}cm"
            img_result.draw_string(8, y_offset, distance_info, image.COLOR_ORANGE)
            y_offset += 22
        
        # 统计信息
        if config.ENABLE_INNER_SHAPE_STATS and config.SHOW_INNER_SHAPE_STATS:
            y_offset = Renderer._draw_stats_info(img_result, y_offset)

        # 拐点分析信息
        if config.ENABLE_CORNER_PROCESSING and config.SHOW_CORNER_ANALYSIS:
            y_offset = Renderer._draw_corner_analysis_info(img_result, y_offset)

        return y_offset
    
    @staticmethod
    def _draw_stats_info(img_result, y_offset):
        """绘制统计信息"""
        stats_map = [
            ('triangles', 'Triangles', image.COLOR_RED),
            ('quadrilaterals', 'Quads', image.COLOR_GREEN),
            ('circles', 'Circles', image.COLOR_BLUE),
            ('polygons', 'Polygons', image.COLOR_PURPLE)
        ]
        
        for stats_key, label, color in stats_map:
            stats = state.inner_shapes_stats[stats_key]
            if stats['count'] > 0:
                if stats_key == 'circles':
                    info = f"{label}: {stats['count']}, Avg R: {stats['avg_radius_physical']:.2f}cm"
                else:
                    info = f"{label}: {stats['count']}, Avg: {stats['avg_edge_length_physical']:.2f}cm"
                img_result.draw_string(8, y_offset, info, color)
                y_offset += 22
        
        return y_offset

    @staticmethod
    def _draw_corner_analysis_info(img_result, y_offset):
        """绘制拐点分析信息"""
        if not state.corner_analysis_results:
            return y_offset

        # 显示拐点分析结果总数
        corner_count = len(state.corner_analysis_results)
        corner_info = f"Corner Analysis: {corner_count} polygons"
        img_result.draw_string(8, y_offset, corner_info, image.COLOR_YELLOW)
        y_offset += 22

        # 显示最小边长信息（取所有多边形中的最小值）
        min_edges = []
        for corner_key, analysis in state.corner_analysis_results.items():
            if analysis['min_edge_physical'] > 0:
                min_edges.append(analysis['min_edge_physical'])

        if min_edges:
            overall_min_edge = min(min_edges)
            min_edge_info = f"Min Edge: {overall_min_edge:.2f}cm"
            img_result.draw_string(8, y_offset, min_edge_info, image.COLOR_YELLOW)
            y_offset += 22

        return y_offset

    @staticmethod
    def highlight_max_rectangles(img_result):
        """高亮显示最大矩形"""
        if not state.max_rectangles or len(state.max_rectangles) < 1:
            return
        
        # 在图像上绘制最大矩形的轮廓
        _, _, approx = state.max_rectangles[0]
        # 转换为点列表
        points = [tuple(pt[0]) for pt in approx]
        # 使用红色标记矩形
        highlight_color = image.COLOR_RED
        
        # 绘制粗线条轮廓
        for j in range(len(points)):
            pt1 = points[j]
            pt2 = points[(j+1) % len(points)]
            img_result.draw_line(pt1[0], pt1[1], pt2[0], pt2[1], highlight_color, 3)
        
        # 在矩形上标注
        M = cv2.moments(approx)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            img_result.draw_string(cx-30, cy, f"标定矩形", highlight_color)
        
        # 绘制ROI边界（如果启用ROI优化）
        if config.USE_ROI_OPTIMIZATION and state.roi_valid and state.cached_roi_rect:
            x, y, w, h = state.cached_roi_rect
            # 绘制ROI边界矩形
            img_result.draw_line(x, y, x+w, y, image.COLOR_GREEN, 2)  # 上边
            img_result.draw_line(x+w, y, x+w, y+h, image.COLOR_GREEN, 2)  # 右边
            img_result.draw_line(x+w, y+h, x, y+h, image.COLOR_GREEN, 2)  # 下边
            img_result.draw_line(x, y+h, x, y, image.COLOR_GREEN, 2)  # 左边
            # 在ROI左上角标注
            img_result.draw_string(x+5, y+5, "ROI", image.COLOR_GREEN)

# ==================== 流程函数区 ====================

class PreprocessingManager:
    """预处理管理器"""
    @staticmethod
    def should_start_preprocessing():
        """是否应该开始预处理"""
        return (config.ENABLE_PREPROCESS and 
                state.frame_count >= config.PREPROCESS_START_FRAME and 
                not state.preprocess_started)
    
    @staticmethod
    def find_max_rectangles(contours):
        """查找最大矩形"""
        if len(state.max_rectangles) >= 1:
            return True
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < config.MIN_AREA:
                continue
            
            epsilon = config.EPSILON_FACTOR * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            
            if len(approx) == 4:
                # 检查重复
                is_duplicate = False
                for _, existing_area, existing_approx in state.max_rectangles:
                    M1, M2 = cv2.moments(approx), cv2.moments(existing_approx)
                    if M1["m00"] != 0 and M2["m00"] != 0:
                        cx1 = int(M1["m10"] / M1["m00"])
                        cy1 = int(M1["m01"] / M1["m00"])
                        cx2 = int(M2["m10"] / M2["m00"])
                        cy2 = int(M2["m01"] / M2["m00"])
                        
                        distance = MathUtils.calculate_distance((cx1, cy1), (cx2, cy2))
                        area_ratio = MathUtils.calculate_area_ratio(area, existing_area)
                        
                        if (distance < config.DUPLICATE_DISTANCE_THRESHOLD and 
                            area_ratio > config.DUPLICATE_AREA_RATIO):
                            is_duplicate = True
                            break
                
                if not is_duplicate:
                    state.max_rectangles.append((contour, area, approx))
                    state.max_rectangles = sorted(state.max_rectangles, key=lambda x: x[1], reverse=True)[:1]
                    print(f"找到标定矩形，面积: {area}")
        
        return len(state.max_rectangles) == 1
    
    @staticmethod
    def update_masks_and_roi():
        """更新掩码和ROI"""
        if len(state.max_rectangles) != 1:
            return False
        
        # 检查是否需要更新
        current_centers = []
        _, _, approx = state.max_rectangles[0]
        M = cv2.moments(approx)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            current_centers.append((cx, cy))
        
        need_update = (not state.cached_mask_valid or 
                      len(state.last_rectangles_centers) != len(current_centers))
        
        if not need_update and len(state.last_rectangles_centers) == len(current_centers):
            for i, (cx, cy) in enumerate(current_centers):
                if i < len(state.last_rectangles_centers):
                    last_cx, last_cy = state.last_rectangles_centers[i]
                    distance = MathUtils.calculate_distance((cx, cy), (last_cx, last_cy))
                    if distance > 10:  # rectangle_position_threshold
                        need_update = True
                        break
        
        if need_update:
            try:
                # 创建掩码
                state.cached_mask = np.zeros((config.CAMERA_HEIGHT, config.CAMERA_WIDTH), dtype=np.uint8)
                _, _, approx = state.max_rectangles[0]
                cv2.drawContours(state.cached_mask, [approx], 0, 255, -1)
                
                mask_pixels = np.sum(state.cached_mask > 0)
                if mask_pixels < 100:
                    state.cached_mask_valid = False
                    state.preprocess_stable_frames = 0
                    return False
                
                state.cached_mask_valid = True
                state.last_rectangles_centers = current_centers.copy()
                
                # 计算ROI
                if config.USE_ROI_OPTIMIZATION:
                    all_points = []
                    for point in approx:
                        all_points.append(point[0])
                    
                    if all_points:
                        all_points = np.array(all_points)
                        min_x = max(0, np.min(all_points[:, 0]) - config.ROI_EXPAND_PIXELS)
                        max_x = min(config.CAMERA_WIDTH, np.max(all_points[:, 0]) + config.ROI_EXPAND_PIXELS)
                        min_y = max(0, np.min(all_points[:, 1]) - config.ROI_EXPAND_PIXELS)
                        max_y = min(config.CAMERA_HEIGHT, np.max(all_points[:, 1]) + config.ROI_EXPAND_PIXELS)
                        
                        state.cached_roi_rect = (int(min_x), int(min_y), int(max_x - min_x), int(max_y - min_y))
                        state.roi_valid = True
                
                state.preprocess_stable_frames += 1
                return True
                
            except Exception as e:
                print(f"掩码创建出错: {e}")
                return False
        else:
            state.preprocess_stable_frames += 1
            return True
    
    @staticmethod
    def filter_contours_by_mask(contours):
        """通过掩码过滤轮廓"""
        if not state.cached_mask_valid or state.preprocess_stable_frames < config.PREPROCESS_STABLE_THRESHOLD:
            return contours
        
        filtered_contours = []
        for contour in contours:
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx, cy = int(M["m10"] / M["m00"]), int(M["m01"] / M["m00"])
                if (0 <= cy < state.cached_mask.shape[0] and 
                    0 <= cx < state.cached_mask.shape[1] and 
                    state.cached_mask[cy, cx] > 0):
                    filtered_contours.append(contour)
        
        return filtered_contours

class ShapeProcessingPipeline:
    """形状处理流水线"""
    @staticmethod
    def detect_all_shapes(contours):
        """检测所有形状"""
        shape_candidates = {'Triangle': [], 'Quad': [], 'Circle': [], 'Polygon': []}
        detected_shapes = []
        
        for contour in contours:
            shape_info = ShapeDetector.classify_shape(contour)
            if shape_info and not ShapeDetector.is_duplicate_shape(shape_info, detected_shapes):
                shape_type = shape_info['type']
                if shape_type in shape_candidates:
                    shape_candidates[shape_type].append(shape_info)
                    detected_shapes.append((shape_info['cx'], shape_info['cy'], 
                                          shape_info['area'], shape_info['vertices_count']))
        
        return shape_candidates, detected_shapes
    
    @staticmethod
    def detect_polygons_in_quad_region(quad_approx, img_cv, edges, min_area, detected_shapes, 
                                     duplicate_distance_threshold, duplicate_area_ratio, img_result, 
                                     frame_count, shape_tracking_data, vertex_history, vertex_history_size, 
                                     position_tolerance, last_frame_shapes):
        """在四边形区域内检测多边形，使用更精细的epsilon_factor"""
        # 只在预处理完成后才进行多边形检测
        if not (config.ENABLE_PREPROCESS and state.preprocess_started and 
                len(state.max_rectangles) == 1 and 
                state.preprocess_stable_frames >= config.PREPROCESS_STABLE_THRESHOLD):
            return
        
        # ROI模式下检查四边形是否在ROI区域内
        if (config.USE_ROI_OPTIMIZATION and state.roi_valid and state.cached_roi_rect is not None and
            edges.shape != img_cv.shape[:2]):
            roi_x, roi_y, roi_w, roi_h = state.cached_roi_rect
            
            # 检查四边形的所有顶点是否都在ROI区域内
            all_vertices_in_roi = True
            for point in quad_approx:
                px, py = point[0]
                if not (roi_x <= px < roi_x + roi_w and roi_y <= py < roi_y + roi_h):
                    all_vertices_in_roi = False
                    break
            
            if not all_vertices_in_roi:
                return
        
        # 创建四边形区域的掩码
        if edges.shape != img_cv.shape[:2]:
            # ROI模式：调整quad_approx坐标
            if state.roi_valid and state.cached_roi_rect is not None:
                roi_x, roi_y, roi_w, roi_h = state.cached_roi_rect
                adjusted_quad_approx = quad_approx.copy()
                adjusted_quad_approx[:, 0, 0] -= roi_x
                adjusted_quad_approx[:, 0, 1] -= roi_y
                
                mask = np.zeros(edges.shape, dtype=np.uint8)
                cv2.fillPoly(mask, [adjusted_quad_approx], 255)
                
                # 扩展四边形区域以避免边缘效应
                if config.QUAD_MASK_EXPAND_PIXELS > 0:
                    kernel = np.ones((config.QUAD_MASK_EXPAND_PIXELS*2+1, config.QUAD_MASK_EXPAND_PIXELS*2+1), np.uint8)
                    mask = cv2.dilate(mask, kernel, iterations=1)
            else:
                return
        else:
            # 全图模式：直接使用原坐标
            mask = np.zeros(img_cv.shape[:2], dtype=np.uint8)
            cv2.fillPoly(mask, [quad_approx], 255)
            
            # 扩展四边形区域以避免边缘效应
            if config.QUAD_MASK_EXPAND_PIXELS > 0:
                kernel = np.ones((config.QUAD_MASK_EXPAND_PIXELS*2+1, config.QUAD_MASK_EXPAND_PIXELS*2+1), np.uint8)
                mask = cv2.dilate(mask, kernel, iterations=1)
        
        # 在四边形区域内查找轮廓
        masked_edges = cv2.bitwise_and(edges, mask)
        contours, _ = cv2.findContours(masked_edges, cv2.RETR_TREE, cv2.CHAIN_APPROX_NONE)
        
        # 使用更精细的epsilon_factor
        fine_epsilon_factor = 0.01
        
        try:
            for contour in contours:
                area = cv2.contourArea(contour)
                if area < min_area:
                    continue
                
                # 使用精细的epsilon_factor进行轮廓近似
                epsilon = fine_epsilon_factor * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                num_vertices = len(approx)
                
                # 只检测多边形（5个或更多顶点）
                if num_vertices >= 5:
                    M = cv2.moments(contour)
                    if M["m00"] == 0:
                        continue
                    
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    
                    # 如果是ROI模式，需要将坐标转换回原图坐标系
                    if edges.shape != img_cv.shape[:2] and state.roi_valid and state.cached_roi_rect is not None:
                        roi_x, roi_y, roi_w, roi_h = state.cached_roi_rect
                        cx += roi_x
                        cy += roi_y
                        # 调整approx中的顶点坐标
                        adjusted_approx = approx.copy()
                        adjusted_approx[:, 0, 0] += roi_x
                        adjusted_approx[:, 0, 1] += roi_y
                        approx = adjusted_approx
                    
                    # 检查是否是重复的多边形
                    is_duplicate = ShapeDetector.is_duplicate_shape(
                        {'cx': cx, 'cy': cy, 'area': area, 'vertices_count': num_vertices}, 
                        detected_shapes
                    )
                    
                    if is_duplicate:
                        continue
                    
                    shape = f"Polygon({num_vertices})"
                    color = image.COLOR_PURPLE
                    
                    # 添加到已检测形状列表
                    detected_shapes.append((cx, cy, area, num_vertices))
                    
                    # 在图像上标记识别结果
                    img_result.draw_string(cx-20, cy, shape, color)
                    if config.SHOW_SHAPE_AREAS:
                        area_text = f"A:{int(area)}"
                        img_result.draw_string(cx-20, cy+15, area_text, color)
                    
                    # 提取顶点坐标列表
                    vertices = [tuple(pt[0]) for pt in approx]
                    
                    # 查找匹配的形状位置并更新跟踪数据
                    shape_position = ShapeTracker.find_matching_position(cx, cy, "Polygon")
                    shape_position = ShapeTracker.update_tracking({
                        'type': 'Polygon', 'cx': cx, 'cy': cy, 'area': area, 'vertices': vertices
                    })
                    
                    # 记录该多边形到当前帧多边形列表
                    if "Polygon" not in last_frame_shapes:
                        last_frame_shapes["Polygon"] = []
                    last_frame_shapes["Polygon"].append((cx, cy, area))
                    
                    # 更新框内图形统计数据
                    if (config.ENABLE_INNER_SHAPE_STATS and config.ENABLE_PREPROCESS and
                        state.preprocess_started and len(state.max_rectangles) == 1 and
                        state.preprocess_stable_frames >= config.PREPROCESS_STABLE_THRESHOLD):
                        StatsUpdater.update_shape_stats({
                            'type': 'Polygon', 'vertices': vertices
                        })

                    # 拐点处理（如果启用）
                    if config.ENABLE_CORNER_PROCESSING and num_vertices >= 5:
                        try:
                            # 获取参考像素长度
                            reference_pixels = DistanceCalculator.get_max_rect_longest_edge()
                            if reference_pixels > 0:
                                # 提取噪声点（如果缓存为空）
                                if not state.noise_points_cache:
                                    state.noise_points_cache = CornerProcessor.extract_noise_points_from_contour(
                                        contour, masked_edges
                                    )

                                # 处理拐点
                                min_edge_pixels, min_edge_physical, corner_marks, min_edge_indices = \
                                    CornerProcessor.process_polygon_corners_and_find_min_edge(
                                        approx, reference_pixels, config.MAX_RECT_PHYSICAL_LENGTH,
                                        config.CORNER_OVERLAP_THRESHOLD, config.ENABLE_ISOLATED_CORNER_DETECTION,
                                        state.noise_points_cache
                                    )

                                # 存储拐点分析结果
                                corner_key = f"polygon_{cx}_{cy}"
                                state.corner_analysis_results[corner_key] = {
                                    'min_edge_pixels': min_edge_pixels,
                                    'min_edge_physical': min_edge_physical,
                                    'corner_marks': corner_marks,
                                    'min_edge_indices': min_edge_indices,
                                    'vertices': vertices
                                }

                                # 显示拐点分析结果（如果启用）
                                if config.SHOW_CORNER_ANALYSIS and min_edge_physical > 0:
                                    corner_text = f"MinEdge:{min_edge_physical:.2f}cm"
                                    img_result.draw_string(cx-40, cy-20, corner_text, image.COLOR_YELLOW)

                        except Exception as e:
                            print(f"拐点处理出错: {e}")

                    # 画出轮廓和拐点，并显示边长（完整的边长绘制功能）
                    if vertices:
                        for i in range(len(vertices)):
                            pt1, pt2 = vertices[i], vertices[(i+1) % len(vertices)]
                            
                            # 绘制边
                            img_result.draw_line(pt1[0], pt1[1], pt2[0], pt2[1], color, 2)
                            img_result.draw_circle(pt1[0], pt1[1], 3, color, thickness=-1)
                            
                            # 计算并显示边长
                            if config.SHOW_EDGE_LENGTHS:
                                edge_length = int(MathUtils.calculate_distance(pt1, pt2))
                                edge_key = (shape_position, i)
                                
                                if edge_key not in state.edge_history:
                                    state.edge_history[edge_key] = []
                                
                                state.edge_history[edge_key].append(edge_length)
                                if len(state.edge_history[edge_key]) > config.EDGE_HISTORY_SIZE:
                                    state.edge_history[edge_key].pop(0)
                                
                                display_length = UtilityFunctions.get_value_from_history(
                                    state.edge_history[edge_key], config.USE_INSTANT_VALUES
                                )
                                mid_x, mid_y = (pt1[0] + pt2[0]) // 2, (pt1[1] + pt2[1]) // 2
                                img_result.draw_string(mid_x, mid_y, str(display_length), image.COLOR_RED)
        
        except Exception as e:
            print(f"四边形区域多边形检测出错: {e}")
    
    @staticmethod
    def apply_overlap_filters(shape_candidates):
        """应用基于优先级的重叠过滤"""
        print("🎯 开始应用优先级过滤体系...")
        print(f"过滤前形状数量: 三角形={len(shape_candidates['Triangle'])}, 四边形={len(shape_candidates['Quad'])}, 圆形={len(shape_candidates['Circle'])}, 多边形={len(shape_candidates['Polygon'])}")
        
        # 使用新的优先级过滤系统
        filtered_results = ShapeFilter.apply_priority_based_filtering(shape_candidates)
        
        print(f"优先级过滤后形状数量: 三角形={len(filtered_results['Triangle'])}, 四边形={len(filtered_results['Quad'])}, 圆形={len(filtered_results['Circle'])}, 多边形={len(filtered_results['Polygon'])}")
        
        return filtered_results
    
    @staticmethod
    def process_and_render_shapes(filtered_shapes, img_result, img_cv, edges):
        """处理和渲染形状"""
        shape_counts = {'Triangle': 0, 'Quad': 0, 'Circle': 0, 'Polygon': 0}
        
        # 重置统计
        StatsUpdater.reset_stats()
        
        for shape_type, shapes in filtered_shapes.items():
            for shape_info in shapes:
                # 更新跟踪
                shape_position = ShapeTracker.update_tracking(shape_info)
                
                # 距离测量（仅对四边形）
                is_max_rect = False
                if shape_type == 'Quad':
                    is_max_rect = ShapeProcessingPipeline._is_max_rectangle(shape_info)
                    
                    if is_max_rect:
                        distance = DistanceMeasurement.calculate_distance(shape_info)
                        if distance:
                            distance_text = f"Dist:{distance:.1f}cm"
                            img_result.draw_string(shape_info['cx']-30, shape_info['cy']-30, 
                                                 distance_text, image.COLOR_ORANGE)
                
                # 更新统计
                if (state.preprocess_started and len(state.max_rectangles) == 1 and 
                    state.preprocess_stable_frames >= config.PREPROCESS_STABLE_THRESHOLD):
                    StatsUpdater.update_shape_stats(shape_info)
                
                # 渲染形状
                Renderer.draw_shape(img_result, shape_info, shape_position, is_max_rect, False)
                shape_counts[shape_type] += 1
                
                # 对保留的四边形进行精细多边形检测（移除条件限制，传递正确的detected_shapes）
                if (shape_type == 'Quad' and config.ENABLE_PREPROCESS and 
                    state.preprocess_started and len(state.max_rectangles) == 1 and 
                    state.preprocess_stable_frames >= config.PREPROCESS_STABLE_THRESHOLD):
                    
                    # 创建累积的detected_shapes列表（包含所有已处理的形状）
                    accumulated_detected_shapes = []
                    for processed_type, processed_shapes in filtered_shapes.items():
                        for processed_shape in processed_shapes:
                            accumulated_detected_shapes.append((
                                processed_shape['cx'], processed_shape['cy'], 
                                processed_shape['area'], processed_shape['vertices_count']
                            ))
                    
                    # 对所有四边形进行多边形检测（移除is_max_rect限制）
                    ShapeProcessingPipeline.detect_polygons_in_quad_region(
                        shape_info['approx'], img_cv, edges, config.MIN_AREA, 
                        accumulated_detected_shapes, config.DUPLICATE_DISTANCE_THRESHOLD, config.DUPLICATE_AREA_RATIO,
                        img_result, state.frame_count, state.shape_tracking_data, 
                        state.vertex_history, config.VERTEX_HISTORY_SIZE, 
                        config.POSITION_TOLERANCE, state.last_frame_shapes
                    )
        
        return shape_counts
    
    @staticmethod
    def _is_max_rectangle(quad_info):
        """检查四边形是否是最大矩形"""
        if not state.max_rectangles or len(state.max_rectangles) < 1:
            return False
        
        quad_cx, quad_cy, quad_area = quad_info['cx'], quad_info['cy'], quad_info['area']
        
        _, max_area, max_approx = state.max_rectangles[0]
        # 计算最大矩形的中心点
        M_max = cv2.moments(max_approx)
        if M_max["m00"] != 0:
            cx_max = int(M_max["m10"] / M_max["m00"])
            cy_max = int(M_max["m01"] / M_max["m00"])
            
            # 计算距离和面积比
            distance = MathUtils.calculate_distance((quad_cx, quad_cy), (cx_max, cy_max))
            area_ratio = MathUtils.calculate_area_ratio(quad_area, max_area)
            
            # 判断是否是同一个矩形
            if (distance < config.DUPLICATE_DISTANCE_THRESHOLD and 
                area_ratio > config.DUPLICATE_AREA_RATIO):
                return True
        
        return False

def create_display_image(img_cv, binary, edges):
    """创建显示图像"""
    if not config.SHOW_DEBUG:
        return image.cv2image(img_cv, bgr=True, copy=False)
    
    if config.DEBUG_VIEW == 0:
        return image.cv2image(img_cv, bgr=True, copy=False)
    elif config.DEBUG_VIEW == 1:
        # 处理ROI二值图像
        if (config.USE_ROI_OPTIMIZATION and state.roi_valid and 
            state.preprocess_started and len(state.max_rectangles) == 1 and 
            state.preprocess_stable_frames >= config.PREPROCESS_STABLE_THRESHOLD):
            full_binary = np.zeros((config.CAMERA_HEIGHT, config.CAMERA_WIDTH), dtype=np.uint8)
            x, y, w, h = state.cached_roi_rect
            if binary.shape[0] == h and binary.shape[1] == w:
                full_binary[y:y+h, x:x+w] = binary
            else:
                full_binary = binary if binary.shape == (config.CAMERA_HEIGHT, config.CAMERA_WIDTH) else full_binary
            binary_colored = np.stack([full_binary, full_binary, full_binary], axis=2)
        else:
            binary_colored = np.stack([binary, binary, binary], axis=2)
        return image.cv2image(binary_colored)
    elif config.DEBUG_VIEW == 2:
        # 处理ROI边缘图像
        if (config.USE_ROI_OPTIMIZATION and state.roi_valid and 
            state.preprocess_started and len(state.max_rectangles) == 1 and 
            state.preprocess_stable_frames >= config.PREPROCESS_STABLE_THRESHOLD):
            full_edges = np.zeros((config.CAMERA_HEIGHT, config.CAMERA_WIDTH), dtype=np.uint8)
            x, y, w, h = state.cached_roi_rect
            if edges.shape[0] == h and edges.shape[1] == w:
                full_edges[y:y+h, x:x+w] = edges
            else:
                full_edges = edges if edges.shape == (config.CAMERA_HEIGHT, config.CAMERA_WIDTH) else full_edges
            edges_colored = np.stack([full_edges, full_edges, full_edges], axis=2)
        else:
            edges_colored = np.stack([edges, edges, edges], axis=2)
        return image.cv2image(edges_colored)

def process_single_frame(img_cv):
    """处理单帧图像"""
    # 1. 图像预处理
    roi_rect = state.cached_roi_rect if state.roi_valid else None
    gray, binary, edges, roi_offset = ImageProcessor.preprocess_image(img_cv, roi_rect)
    
    # 2. 轮廓检测
    contours = ImageProcessor.find_contours_with_roi_adjustment(edges, roi_offset)
    contours = sorted(contours, key=cv2.contourArea, reverse=True)
    
    # 3. 预处理管理
    if PreprocessingManager.should_start_preprocessing():
        print("开始预处理：识别最大的标定矩形")
        state.preprocess_started = True
        state.max_rectangles = []
    
    if state.preprocess_started:
        if not PreprocessingManager.find_max_rectangles(contours):
            state.preprocess_stable_frames = 0
        else:
            PreprocessingManager.update_masks_and_roi()
            contours = PreprocessingManager.filter_contours_by_mask(contours)
    
    # 4. 形状检测和过滤
    shape_candidates, detected_shapes = ShapeProcessingPipeline.detect_all_shapes(contours)
    filtered_shapes = ShapeProcessingPipeline.apply_overlap_filters(shape_candidates)
    
    # 5. 创建显示图像
    img_result = create_display_image(img_cv, binary, edges)
    
    # 6. 处理和渲染形状
    shape_counts = ShapeProcessingPipeline.process_and_render_shapes(filtered_shapes, img_result, img_cv, edges)
    
    # 7. 绘制信息覆盖层
    Renderer.draw_info_overlay(img_result)
    
    # 8. 突出显示最大矩形（如果预处理完成）
    if (config.ENABLE_PREPROCESS and state.preprocess_started and 
        len(state.max_rectangles) == 1 and 
        state.preprocess_stable_frames >= config.PREPROCESS_STABLE_THRESHOLD):
        Renderer.highlight_max_rectangles(img_result)
    
    # 9. 清理过期跟踪数据
    cleaned_count = ShapeTracker.cleanup_expired_shapes()
    if cleaned_count > 0:
        print(f"清理了 {cleaned_count} 个过期形状位置")
    
    # 9. 重置帧状态
    state.last_frame_shapes = {}
    
    # 10. 更新统计信息
    StatisticsManager.update_shape_statistics(
        filtered_shapes['Triangle'],
        filtered_shapes['Quad'], 
        filtered_shapes['Circle'],
        filtered_shapes['Polygon']
    )
    
    # 11. 打印统计信息
    total_shapes = sum(shape_counts.values())
    if (state.preprocess_started and len(state.max_rectangles) == 1 and 
        state.preprocess_stable_frames >= config.PREPROCESS_STABLE_THRESHOLD):
        print(f"========== 帧 {state.frame_count} ==========")
        print(f"图形检测: 三角形 {shape_counts['Triangle']} | 四边形 {shape_counts['Quad']} | 圆形 {shape_counts['Circle']} | 多边形 {shape_counts['Polygon']} | 总计 {total_shapes}")
        
        # 显示框内图形统计信息
        if config.ENABLE_INNER_SHAPE_STATS:
            stats_parts = []
            if state.inner_shapes_stats['triangles']['count'] > 0:
                stats_parts.append(f"三角形 {state.inner_shapes_stats['triangles']['count']}个 (平均边长 {state.inner_shapes_stats['triangles']['avg_edge_length_physical']:.1f}cm)")
            if state.inner_shapes_stats['quadrilaterals']['count'] > 0:
                stats_parts.append(f"四边形 {state.inner_shapes_stats['quadrilaterals']['count']}个 (平均边长 {state.inner_shapes_stats['quadrilaterals']['avg_edge_length_physical']:.1f}cm)")
            if state.inner_shapes_stats['circles']['count'] > 0:
                stats_parts.append(f"圆形 {state.inner_shapes_stats['circles']['count']}个 (平均半径 {state.inner_shapes_stats['circles']['avg_radius_physical']:.1f}cm)")
            if state.inner_shapes_stats['polygons']['count'] > 0:
                stats_parts.append(f"多边形 {state.inner_shapes_stats['polygons']['count']}个 (平均边长 {state.inner_shapes_stats['polygons']['avg_edge_length_physical']:.1f}cm)")
            
            if stats_parts:
                print(f"框内统计: " + " | ".join(stats_parts))
        
        if config.ENABLE_DISTANCE_MEASUREMENT and state.current_distance > 0:
            print(f"距离测量: {state.current_distance:.1f}cm")
        
        # 打印详细统计信息
        StatisticsManager.print_detailed_statistics()
    else:
        print(f"========== 帧 {state.frame_count} ==========")
        print(f"预处理中 - 图形检测: 总计 {total_shapes} 个图形")
    
    return img_result

# ==================== 主函数区 ====================

def main():
    """主函数"""
    # 初始化硬件
    cam = camera.Camera(config.CAMERA_WIDTH, config.CAMERA_HEIGHT, image.Format.FMT_BGR888)
    disp = display.Display()
    
    print("形状检测系统启动")
    print(f"分辨率: {config.CAMERA_WIDTH}x{config.CAMERA_HEIGHT}")
    print(f"预处理: {'启用' if config.ENABLE_PREPROCESS else '禁用'}")
    print(f"ROI优化: {'启用' if config.USE_ROI_OPTIMIZATION else '禁用'}")
    print(f"距离测量: {'启用' if config.ENABLE_DISTANCE_MEASUREMENT else '禁用'}")
    
    try:
        while not app.need_exit():
            # 计时开始
            t_start = time.ticks_ms()
            
            # 读取图像
            img_maix = cam.read()
            img_cv = image.image2cv(img_maix, ensure_bgr=True, copy=False)
            
            # 处理单帧
            img_result = process_single_frame(img_cv)
            
            # 显示结果
            disp.show(img_result)
            
            # 更新帧计数
            state.frame_count += 1
            
            # 计时结束（可选）
            # t_end = time.ticks_ms()
            # print(f"帧处理时间: {t_end - t_start}ms")
            
    except KeyboardInterrupt:
        print("程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        print("形状检测系统关闭")

# ==================== 拐点处理类 ====================

class CornerProcessor:
    """拐点处理器 - 处理多边形拐点检测、分类和特殊处理"""

    @staticmethod
    def calculate_edge_overlap_with_polygon(edge_start, edge_end, polygon_points, overlap_threshold=0.5):
        """
        计算边与多边形的重叠程度
        返回重叠比例 (0.0 - 1.0)

        参数:
        - edge_start: 边的起点
        - edge_end: 边的终点
        - polygon_points: 多边形顶点
        - overlap_threshold: 重叠阈值，超过此值认为边在多边形内
        """
        try:
            if edge_start == edge_end:
                print("警告：边的起点和终点相同")
                return 0.0

            # 计算边长，如果太短则返回0
            edge_length = MathUtils.calculate_distance(edge_start, edge_end)
            if edge_length < 1.0:  # 边长小于1像素
                print(f"警告：边长太短 ({edge_length:.2f} pixels)")
                return 0.0

            # 在边上采样多个点进行检测
            # 根据边长动态调整采样点数量，但设置最小和最大值
            sample_count = max(10, min(50, int(edge_length / 2)))  # 10-50个采样点
            inside_count = 0

            for i in range(sample_count + 1):
                t = i / sample_count
                # 线性插值计算采样点
                sample_x = edge_start[0] + t * (edge_end[0] - edge_start[0])
                sample_y = edge_start[1] + t * (edge_end[1] - edge_start[1])
                sample_point = (sample_x, sample_y)

                # 检查采样点是否在多边形内
                if UtilityFunctions.is_point_inside_polygon(sample_point, polygon_points):
                    inside_count += 1

            # 计算重叠比例
            overlap_ratio = inside_count / (sample_count + 1)

            print(f"    边重叠分析: 长度={edge_length:.1f}px, 采样={sample_count+1}点, 内部={inside_count}点, 比例={overlap_ratio:.3f}")
            return overlap_ratio

        except Exception as e:
            print(f"计算边与多边形重叠度出错: {e}")
            return 0.0

    @staticmethod
    def extract_noise_points_from_contour(contour, edges_image):
        """
        提取轮廓内部的噪声点（白色像素点）

        参数:
        - contour: 轮廓点
        - edges_image: 边缘检测图像

        返回:
        - noise_points: 轮廓内部的噪声点列表 [(x, y), ...]
        """
        if edges_image is None or len(contour) < 3:
            return []

        # 获取轮廓边界框
        x, y, w, h = cv2.boundingRect(contour)

        noise_points = []

        # 在边界框内搜索噪声点
        for py in range(y, y + h):
            for px in range(x, x + w):
                # 检查点是否在轮廓内部
                if cv2.pointPolygonTest(contour, (px, py), False) > 0:  # 严格内部
                    # 检查是否为噪声点（边缘检测图像中的白色像素）
                    if edges_image[py, px] > 0:
                        noise_points.append((px, py))

        return noise_points

    @staticmethod
    def find_closest_noise_point_from_list(isolated_point, noise_points):
        """
        从预存储的噪声点列表中找到离孤立拐点最近的噪声点

        参数:
        - isolated_point: 孤立拐点坐标 (x, y)
        - noise_points: 预存储的噪声点列表 [(x, y), ...]

        返回:
        - closest_point: 最近的噪声点坐标 (x, y)，如果没找到返回None
        """
        if not noise_points:
            print(f"        警告：噪声点列表为空")
            return None

        min_distance = float('inf')
        closest_point = None

        for noise_point in noise_points:
            distance = MathUtils.calculate_distance(isolated_point, noise_point)
            if distance < min_distance:
                min_distance = distance
                closest_point = noise_point

        if closest_point:
            print(f"        从 {len(noise_points)} 个预存储噪声点中找到最近点: {closest_point}, 距离: {min_distance:.2f}")

        return closest_point

    @staticmethod
    def handle_single_isolated_corner(vertices, corner_marks, isolated_index, noise_points):
        """
        处理单个孤立拐点的情况

        使用预存储的噪声点，通过几何计算生成有效边
        """
        print(f"      处理单个孤立拐点 (索引 {isolated_index})")

        isolated_point = vertices[isolated_index]

        # 找到前后相邻的两个无效拐点
        prev_index = (isolated_index - 1) % len(vertices)
        next_index = (isolated_index + 1) % len(vertices)

        if corner_marks[prev_index] != 0 or corner_marks[next_index] != 0:
            print(f"        警告：孤立拐点的前后拐点不都是无效拐点")
            return []

        prev_point = vertices[prev_index]
        next_point = vertices[next_index]

        # 从预存储的噪声点中找最近的噪声点
        inner_point = CornerProcessor.find_closest_noise_point_from_list(isolated_point, noise_points)

        if inner_point is None:
            print(f"        警告：未找到预存储的噪声点")
            return []

        print(f"        找到内点（预存储噪声点）: {inner_point}")

        # 构成四边形并计算面积
        quad_points = [isolated_point, prev_point, inner_point, next_point]
        quad_area = UtilityFunctions.calculate_polygon_area(quad_points)

        print(f"        四边形面积: {quad_area:.2f}")

        # 计算内点到两条延长线的垂直距离
        dist1 = UtilityFunctions.point_to_line_distance(inner_point, isolated_point, prev_point)
        dist2 = UtilityFunctions.point_to_line_distance(inner_point, isolated_point, next_point)

        print(f"        垂直距离1: {dist1:.2f}, 垂直距离2: {dist2:.2f}")

        # 计算两个正方形面积
        square_area1 = dist1 * dist1
        square_area2 = dist2 * dist2

        print(f"        正方形面积1: {square_area1:.2f}, 正方形面积2: {square_area2:.2f}")

        # 选择面积更大的正方形的边长
        if square_area1 > quad_area and square_area1 >= square_area2:
            selected_edge = dist1
            print(f"        选择垂直距离1作为有效边: {selected_edge:.2f}")
        elif square_area2 > quad_area:
            selected_edge = dist2
            print(f"        选择垂直距离2作为有效边: {selected_edge:.2f}")
        else:
            print(f"        两个正方形面积都不大于四边形面积，不添加有效边")
            return []

        return [selected_edge]

    @staticmethod
    def handle_two_isolated_corners(vertices, corner_marks, isolated_indices):
        """
        处理两个孤立拐点的情况
        """
        print(f"      处理两个孤立拐点 (索引 {isolated_indices})")

        point1 = vertices[isolated_indices[0]]
        point2 = vertices[isolated_indices[1]]

        # 计算两个孤立拐点之间的距离
        line_length = MathUtils.calculate_distance(point1, point2)
        print(f"        两个孤立拐点间距离: {line_length:.2f}")

        # 检查线段上是否有无效拐点
        invalid_indices = [i for i, mark in enumerate(corner_marks) if mark == 0]
        has_invalid_on_line = False

        for invalid_idx in invalid_indices:
            invalid_point = vertices[invalid_idx]
            if UtilityFunctions.is_point_near_line_segment(invalid_point, point1, point2, threshold=5.0):
                print(f"        发现无效拐点 {invalid_idx} 在线段附近")
                has_invalid_on_line = True
                break

        if has_invalid_on_line:
            print(f"        线段上有无效拐点，使用原始长度: {line_length:.2f}")
            return [line_length]
        else:
            adjusted_length = line_length / np.sqrt(2)
            print(f"        线段上无无效拐点，使用调整后长度: {adjusted_length:.2f} (原长度/{np.sqrt(2):.3f})")
            return [adjusted_length]

    @staticmethod
    def handle_three_isolated_corners(vertices, isolated_indices):
        """
        处理三个孤立拐点的情况
        """
        print(f"      处理三个孤立拐点 (索引 {isolated_indices})")

        # 按照在多边形中的位置顺序确定中间的孤立拐点
        sorted_indices = sorted(isolated_indices)

        # 选择第一个和第三个孤立拐点（跳过中间的）
        point1 = vertices[sorted_indices[0]]
        point3 = vertices[sorted_indices[2]]

        line_length = MathUtils.calculate_distance(point1, point3)

        print(f"        连接拐点 {sorted_indices[0]} 和 {sorted_indices[2]}，忽略中间拐点 {sorted_indices[1]}")
        print(f"        线段长度: {line_length:.2f}")

        return [line_length]

    @staticmethod
    def handle_isolated_corners(vertices, corner_marks, isolated_indices, noise_points):
        """
        处理孤立拐点的特殊情况

        参数:
        - vertices: 多边形顶点列表
        - corner_marks: 拐点标记列表
        - isolated_indices: 孤立拐点索引列表
        - noise_points: 预存储的噪声点列表

        返回:
        - additional_edges: 新增的有效边长列表
        """
        additional_edges = []
        isolated_count = len(isolated_indices)

        print(f"    开始处理 {isolated_count} 个孤立拐点: {isolated_indices}")

        if isolated_count == 1:
            # 情况1：1个孤立拐点
            additional_edges.extend(CornerProcessor.handle_single_isolated_corner(vertices, corner_marks, isolated_indices[0],
                                                                                noise_points))

        elif isolated_count == 2:
            # 情况2：2个孤立拐点
            additional_edges.extend(CornerProcessor.handle_two_isolated_corners(vertices, corner_marks, isolated_indices))

        elif isolated_count == 3:
            # 情况3：3个孤立拐点
            additional_edges.extend(CornerProcessor.handle_three_isolated_corners(vertices, isolated_indices))

        else:
            print(f"    警告：孤立拐点数量为 {isolated_count}，暂不处理")

        return additional_edges

    @staticmethod
    def process_polygon_corners_and_find_min_edge(polygon_approx, reference_pixels, reference_physical_size,
                                                overlap_threshold=0.5, enable_isolated_detection=True, noise_points=None):
        """
        处理多边形拐点，计算最小边长并转换为物理长度

        参数:
        - polygon_approx: 多边形轮廓点
        - reference_pixels: 参考像素长度
        - reference_physical_size: 参考物理长度
        - overlap_threshold: 重叠阈值
        - enable_isolated_detection: 是否启用孤立拐点检测，默认True
        - noise_points: 预存储的噪声点列表，用于孤立拐点处理

        拐点分类:
        - 标记为0: 无效拐点（重叠度 < 阈值）
        - 标记为1: 普通有效拐点（重叠度 >= 阈值，且不是孤立的）
        - 标记为2: 孤立拐点（重叠度 >= 阈值，但前后相邻拐点都无效）

        有效边判断:
        - 只有相邻的拐点都标记为1时，才认为是有效边
        - 标记为2的孤立拐点通过特殊处理后可能产生新的有效边

        孤立拐点特殊处理:
        - 1个孤立拐点: 使用预存储的噪声点，通过内点和垂直距离计算生成有效边
        - 2个孤立拐点: 连接两点，根据线段上是否有无效拐点决定是否调整长度
        - 3个孤立拐点: 连接首尾两个孤立拐点，忽略中间的

        返回:
        - min_edge_length_pixels: 最小边长（像素）
        - min_edge_length_physical: 最小边长（物理长度）
        - corner_marks: 拐点标记列表（0/1/2）
        - min_edge_indices: 最小边的拐点索引
        """
        try:
            if polygon_approx is None or len(polygon_approx) < 3:
                print(f"多边形数据无效: {polygon_approx}")
                return 0.0, 0.0, [], []

            if reference_pixels <= 0 or reference_physical_size <= 0:
                print(f"参考尺寸无效: pixels={reference_pixels}, physical={reference_physical_size}")
                return 0.0, 0.0, [], []

            # 提取顶点坐标
            vertices = []
            for pt in polygon_approx:
                try:
                    if isinstance(pt, np.ndarray):
                        if len(pt.shape) == 2 and pt.shape[0] == 1 and pt.shape[1] >= 2:
                            # OpenCV标准格式: [[x, y]]
                            vertices.append((int(pt[0][0]), int(pt[0][1])))
                        elif len(pt.shape) == 1 and len(pt) >= 2:
                            # 一维数组格式: [x, y]
                            vertices.append((int(pt[0]), int(pt[1])))
                        else:
                            print(f"未知的顶点格式: shape={pt.shape}, 数据={pt}")
                            continue
                    elif isinstance(pt, (list, tuple)) and len(pt) >= 2:
                        # 列表或元组格式
                        if isinstance(pt[0], (list, tuple)):
                            if len(pt[0]) >= 2:
                                # 嵌套格式: [[x, y]]
                                vertices.append((int(pt[0][0]), int(pt[0][1])))
                            else:
                                print(f"嵌套顶点数据长度不足: {pt}")
                                continue
                        else:
                            # 直接格式: [x, y]
                            if len(pt) >= 2:
                                vertices.append((int(pt[0]), int(pt[1])))
                            else:
                                print(f"顶点数据长度不足: {pt}")
                                continue
                    else:
                        print(f"未知的顶点数据类型: {type(pt)}, 数据={pt}")
                        continue
                except (IndexError, ValueError, TypeError) as e:
                    print(f"顶点坐标提取错误: {e}, 数据={pt}")
                    continue

            if len(vertices) < 3:
                print(f"顶点数量不足: {len(vertices)}")
                return 0.0, 0.0, [], []

            print(f"处理多边形拐点: {len(vertices)} 个顶点，重叠阈值: {overlap_threshold}")

            # 第一步：基于重叠度进行初始标记
            initial_marks = []

            for i in range(len(vertices)):
                current_vertex = vertices[i]
                prev_vertex = vertices[(i - 1) % len(vertices)]
                next_vertex = vertices[(i + 1) % len(vertices)]

                print(f"  处理拐点 {i}: 当前{current_vertex}, 前{prev_vertex}, 后{next_vertex}")

                # 计算当前拐点在相邻两点形成的三角形中对应的边
                # 这条边是连接前一个和下一个顶点的边
                edge_start = prev_vertex
                edge_end = next_vertex

                # 计算该边与多边形的重叠程度
                overlap_ratio = CornerProcessor.calculate_edge_overlap_with_polygon(
                    edge_start, edge_end, polygon_approx, overlap_threshold
                )

                # 根据重叠程度进行初始标记
                if overlap_ratio >= overlap_threshold:
                    initial_marks.append(1)
                    print(f"    拐点 {i} 初始标记为 1 (重叠度: {overlap_ratio:.3f} >= {overlap_threshold})")
                else:
                    initial_marks.append(0)
                    print(f"    拐点 {i} 初始标记为 0 (重叠度: {overlap_ratio:.3f} < {overlap_threshold})")

            # 第二步：识别孤立拐点并进行最终标记（如果启用）
            corner_marks = []
            isolated_count = 0

            if enable_isolated_detection:
                print(f"  识别孤立拐点:")
                for i in range(len(vertices)):
                    current_mark = initial_marks[i]
                    prev_mark = initial_marks[(i - 1) % len(vertices)]
                    next_mark = initial_marks[(i + 1) % len(vertices)]

                    # 检查是否为孤立拐点：当前为有效拐点(1)，但前后都是无效拐点(0)
                    if current_mark == 1 and prev_mark == 0 and next_mark == 0:
                        corner_marks.append(2)  # 标记为孤立拐点
                        isolated_count += 1
                        print(f"    拐点 {i} 标记为 2 (孤立拐点: 前{prev_mark}-当前{current_mark}-后{next_mark})")
                    else:
                        corner_marks.append(current_mark)  # 保持原始标记
                        if current_mark == 1:
                            print(f"    拐点 {i} 保持标记为 1 (非孤立: 前{prev_mark}-当前{current_mark}-后{next_mark})")
                        else:
                            print(f"    拐点 {i} 保持标记为 0 (无效拐点)")

                print(f"  识别结果: 发现 {isolated_count} 个孤立拐点")
            else:
                # 如果未启用孤立拐点检测，直接使用初始标记
                corner_marks = initial_marks[:]
                print(f"  孤立拐点检测已禁用，使用初始标记")

            # 查找相邻的有效拐点，计算它们之间的边长
            # 有效拐点包括：标记为1的普通有效拐点和标记为2的孤立拐点
            valid_edges = []
            valid_edge_indices = []

            print(f"  查找相邻的有效拐点（标记为1的拐点）:")
            for i in range(len(vertices)):
                current_mark = corner_marks[i]
                next_mark = corner_marks[(i + 1) % len(vertices)]

                print(f"    检查边 {i}-{(i + 1) % len(vertices)}: 标记 {current_mark}-{next_mark}")

                # 如果当前拐点和下一个拐点都是标记为1的有效拐点
                if current_mark == 1 and next_mark == 1:
                    current_vertex = vertices[i]
                    next_vertex = vertices[(i + 1) % len(vertices)]

                    # 计算边长
                    edge_length = MathUtils.calculate_distance(current_vertex, next_vertex)

                    # 检查边长是否有效
                    if edge_length > 0:
                        valid_edges.append(edge_length)
                        valid_edge_indices.append((i, (i + 1) % len(vertices)))
                        print(f"      有效边 {i}-{(i + 1) % len(vertices)}: {edge_length:.2f} 像素")
                    else:
                        print(f"      警告：边长为0，跳过边 {i}-{(i + 1) % len(vertices)}")

            # 孤立拐点特殊处理
            isolated_indices = [i for i, mark in enumerate(corner_marks) if mark == 2]
            isolated_count = len(isolated_indices)

            print(f"  孤立拐点处理: 发现 {isolated_count} 个孤立拐点")

            if isolated_count > 0:
                additional_edges = CornerProcessor.handle_isolated_corners(vertices, corner_marks, isolated_indices,
                                                                         noise_points)
                if additional_edges:
                    valid_edges.extend(additional_edges)
                    print(f"  孤立拐点处理后，新增 {len(additional_edges)} 条有效边")
                    print(f"  新增边长: {[f'{edge:.2f}' for edge in additional_edges]}")

            if not valid_edges:
                print("  没有找到有效边（相邻的标记为1的拐点）")
                return 0.0, 0.0, corner_marks, []

            # 找出最小边长
            try:
                min_edge_length_pixels = min(valid_edges)
                min_edge_index = valid_edges.index(min_edge_length_pixels)

                # 如果最小边来自原有有效边
                if min_edge_index < len(valid_edge_indices):
                    min_edge_indices = valid_edge_indices[min_edge_index]
                else:
                    min_edge_indices = []  # 来自孤立拐点处理的边

                print(f"  有效边列表: {[f'{edge:.2f}' for edge in valid_edges]}")
                print(f"  最小边长: {min_edge_length_pixels:.2f} 像素 (索引 {min_edge_index})")
                if min_edge_indices:
                    print(f"  最小边连接拐点: {min_edge_indices[0]} - {min_edge_indices[1]}")
                else:
                    print(f"  最小边来自孤立拐点处理")

            except (ValueError, IndexError) as e:
                print(f"查找最小边长时出错: {e}")
                return 0.0, 0.0, corner_marks, []

            # 转换为物理长度
            try:
                min_edge_length_physical = MathUtils.calculate_physical_size(
                    min_edge_length_pixels, reference_pixels, reference_physical_size
                )

                print(f"  物理长度转换: {min_edge_length_pixels:.2f}px × {reference_physical_size:.2f}cm / {reference_pixels:.2f}px = {min_edge_length_physical:.2f}cm")

            except Exception as e:
                print(f"物理长度转换出错: {e}")
                return min_edge_length_pixels, 0.0, corner_marks, min_edge_indices

            return min_edge_length_pixels, min_edge_length_physical, corner_marks, min_edge_indices

        except Exception as e:
            print(f"处理多边形拐点时出错: {e}")
            return 0.0, 0.0, [], []

if __name__ == "__main__":
    main()
